# Token Selector Implementation

This document describes the token selector feature that allows players to select any token supported by the game contract for betting.

## Features

- ✅ Displays all tokens fetched via `getCasinoTokensFunctionData` by default
- ✅ App integrators can optionally pass a filtered token array to limit choices  
- ✅ Shows token symbol and icon (via OnchainKit's `TokenImage`)
- ✅ Updates bet calculations and VRF fees when token changes
- ✅ Remembers selection during the session (sessionStorage)
- ✅ Falls back to configured `bankrollToken` if no selection made

## Components

### `useTokens` Hook

Fetches casino tokens with optional filtering:

```typescript
import { useTokens } from "../hooks/useTokens"

const { tokens, loading, error } = useTokens({
  onlyActive: true,
  filteredTokens: [usdcToken, ethToken] // Optional filtering
})
```

### `TokenSelector` Component

UI component using OnchainKit's `TokenSelectDropdown`:

```typescript
import { TokenSelector } from "../components/ui/TokenSelector"

<TokenSelector
  selectedToken={selectedToken}
  onTokenSelect={setSelectedToken}
  filteredTokens={filteredTokens} // Optional
  className="min-w-[120px]"
/>
```

### Updated Game Components

All game components now support token selection:

```typescript
// CoinTossGame, KenoGame, DiceGame, RouletteGame
<CoinTossGame
  filteredTokens={[usdcToken, ethToken]} // Optional filtering
  // ... other props
/>
```

## Integration

### Basic Usage (All Available Tokens)

```typescript
import { CoinTossGame } from "@betswirl/ui"

function App() {
  return (
    <CoinTossGame
      // Token selector will show all available casino tokens
    />
  )
}
```

### Filtered Tokens Usage

```typescript
import { CoinTossGame } from "@betswirl/ui"
import { TokenWithImage } from "@betswirl/ui/types"

const ALLOWED_TOKENS: TokenWithImage[] = [
  {
    address: "******************************************",
    symbol: "USDC",
    decimals: 6,
    image: "https://...",
    chainId: 8453,
  },
  // ... more tokens
]

function App() {
  return (
    <CoinTossGame
      filteredTokens={ALLOWED_TOKENS}
    />
  )
}
```

## Technical Details

### Session Storage

Selected tokens are persisted per game type:
- Key format: `betswirl-selected-token-${gameType}`
- Automatically restored on page reload
- Falls back to `bankrollToken` if no selection

### Token Selection Flow

1. User selects token from dropdown
2. Token is saved to session storage
3. Game logic updates to use selected token
4. Bet calculations and VRF fees recalculated
5. Token allowances checked for new token

### Error Handling

- Loading states during token fetch
- Error messages for failed token loading
- Graceful fallback to bankroll token
- Empty state when no tokens available

## UI Placement

The token selector is positioned between the game title and connect wallet button in the header:

```
[Game Title] [Token Selector] ..................... [Connect Wallet]
```

This provides easy access while maintaining clean UI hierarchy.
