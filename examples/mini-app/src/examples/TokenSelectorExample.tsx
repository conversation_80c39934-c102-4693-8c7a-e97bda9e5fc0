import { useState } from "react"
import { TokenWithImage } from "../types/types"
import { TokenSelector } from "../components/ui/TokenSelector"

// Example filtered tokens (optional)
const EXAMPLE_FILTERED_TOKENS: TokenWithImage[] = [
  {
    address: "******************************************",
    symbol: "DEGEN",
    decimals: 18,
    image: "https://www.betswirl.com/img/tokens/DEGEN.svg",
    chainId: 8453,
  },
  {
    address: "******************************************",
    symbol: "USDC",
    decimals: 6,
    image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2",
    chainId: 8453,
  },
]

/**
 * Example component demonstrating TokenSelector usage
 */
export function TokenSelectorExample() {
  const [selectedToken, setSelectedToken] = useState<TokenWithImage | undefined>()
  const [useFiltering, setUseFiltering] = useState(false)

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4">Token Selector Example</h2>
      
      <div className="mb-4">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={useFiltering}
            onChange={(e) => setUseFiltering(e.target.checked)}
          />
          Use filtered tokens (DEGEN, USDC only)
        </label>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Select Token:
        </label>
        <TokenSelector
          selectedToken={selectedToken}
          onTokenSelect={setSelectedToken}
          filteredTokens={useFiltering ? EXAMPLE_FILTERED_TOKENS : undefined}
          className="w-full"
        />
      </div>

      {selectedToken && (
        <div className="mt-4 p-3 bg-gray-100 rounded">
          <h3 className="font-medium mb-2">Selected Token:</h3>
          <div className="text-sm">
            <p><strong>Symbol:</strong> {selectedToken.symbol}</p>
            <p><strong>Address:</strong> {selectedToken.address}</p>
            <p><strong>Decimals:</strong> {selectedToken.decimals}</p>
            <p><strong>Chain ID:</strong> {selectedToken.chainId}</p>
          </div>
        </div>
      )}
    </div>
  )
}
