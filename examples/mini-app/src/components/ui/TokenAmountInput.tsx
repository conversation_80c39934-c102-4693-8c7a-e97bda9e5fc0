import { formatRaw<PERSON>mount, FORMAT_TYPE } from "@betswirl/sdk-core"
import { ChevronDown } from "lucide-react"
import { useState, useRef, useEffect } from "react"
import { createPortal } from "react-dom"
import { useBalance, useAccount } from "wagmi"
import { zeroAddress, type Hex } from "viem"
import { cn } from "../../lib/utils"
import { TokenWithImage } from "../../types/types"
import { useTokens } from "../../hooks/useTokens"
import { TokenIcon } from "./TokenIcon"

interface TokenAmountInputProps {
  value: string
  onChange: (value: string) => void
  selectedToken: TokenWithImage
  onTokenSelect: (token: TokenWithImage) => void
  filteredTokens?: TokenWithImage[]
  placeholder?: string
  disabled?: boolean
  className?: string
  min?: number
  max?: number
  step?: number
}

export function TokenAmountInput({
  value,
  onChange,
  selectedToken,
  onTokenSelect,
  filteredTokens,
  placeholder = "0",
  disabled = false,
  className,
  min,
  max,
  step,
}: TokenAmountInputProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 })
  const inputRef = useRef<HTMLDivElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { address } = useAccount()
  const { tokens } = useTokens({
    onlyActive: true,
    filteredTokens,
  })

  // Update dropdown position when opened
  useEffect(() => {
    if (isDropdownOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect()
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      })
    }
  }, [isDropdownOpen])

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDropdownOpen])

  const handleTokenSelect = (token: TokenWithImage) => {
    onTokenSelect(token)
    setIsDropdownOpen(false)
  }

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <div className="relative flex h-12 w-full items-center text-sm">
        <input
          type="number"
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className={cn(
            "flex h-full w-full rounded-[12px] border-0",
            "bg-neutral-background text-foreground font-semibold",
            "px-4 py-2 pr-20",
            "text-base placeholder:text-muted-foreground",
            "ring-offset-background focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-0",
            "disabled:cursor-not-allowed disabled:opacity-50",
          )}
        />

        {/* Token selector button */}
        <button
          type="button"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          disabled={disabled}
          className={cn(
            "absolute right-0 top-1/2 mr-3 flex -translate-y-1/2 transform items-center gap-1",
            "text-foreground font-medium cursor-pointer hover:opacity-80",
            "disabled:cursor-not-allowed disabled:opacity-50"
          )}
        >
          <TokenIcon token={selectedToken} size={18} />
          <span>{selectedToken.symbol}</span>
          <ChevronDown className={cn(
            "h-4 w-4 transition-transform",
            isDropdownOpen && "rotate-180"
          )} />
        </button>
      </div>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className={cn(
          "absolute top-full left-0 right-0 z-50 mt-1",
          "bg-neutral-background border border-border-stroke rounded-[12px]",
          "shadow-lg max-h-60 overflow-y-auto"
        )}>
          {tokens.map((token) => (
            <TokenOption
              key={token.address}
              token={token}
              isSelected={token.address === selectedToken.address}
              onSelect={() => handleTokenSelect(token)}
              userAddress={address}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface TokenOptionProps {
  token: TokenWithImage
  isSelected: boolean
  onSelect: () => void
  userAddress?: string
}

function TokenOption({ token, isSelected, onSelect, userAddress }: TokenOptionProps) {
  const { data: balance } = useBalance({
    address: userAddress as Hex,
    token: token.address === zeroAddress ? undefined : (token.address as Hex),
  })

  const formattedBalance = balance
    ? formatRawAmount(balance.value, token.decimals, FORMAT_TYPE.PRECISE)
    : "0"

  return (
    <button
      type="button"
      onClick={onSelect}
      className={cn(
        "w-full flex items-center justify-between px-4 py-3",
        "hover:bg-muted/50 transition-colors",
        "first:rounded-t-[12px] last:rounded-b-[12px]",
        isSelected && "bg-muted"
      )}
    >
      <div className="flex items-center gap-2">
        <TokenIcon token={token} size={20} />
        <span className="font-medium">{token.symbol}</span>
      </div>
      <span className="text-sm text-muted-foreground">{formattedBalance}</span>
    </button>
  )
}
